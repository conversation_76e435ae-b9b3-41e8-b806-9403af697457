import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { useCompany } from '@/contexts/CompanyContext';

export interface Appointment {
  id: string;
  title: string;
  description: string | null;
  start_time: string;
  end_time: string;
  location: string | null;
  lead_id: string | null;
  user_id: string;
  company_id: string;
  external_event_id: string | null;
  calendar_provider: string | null;
  sync_status: string;
  created_at: string;
  updated_at: string;
}

export const useAppointments = () => {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { currentCompany } = useCompany();

  const fetchAppointments = async () => {
    if (!user || !currentCompany?.id) return;

    try {
      const { data, error } = await supabase
        .from('appointments')
        .select('*')
        .eq('company_id', currentCompany.id)
        .order('start_time', { ascending: true });

      if (error) throw error;
      setAppointments(data || []);
    } catch (error) {
      console.error('Error fetching appointments:', error);
    } finally {
      setLoading(false);
    }
  };

  const createAppointment = async (appointmentData: Omit<Appointment, 'id' | 'user_id' | 'company_id' | 'created_at' | 'updated_at' | 'sync_status'>) => {
    if (!user || !currentCompany?.id) throw new Error('User not authenticated');

    try {
      const { data, error } = await supabase
        .from('appointments')
        .insert({
          ...appointmentData,
          user_id: user.id,
          company_id: currentCompany.id,
          sync_status: 'pending'
        })
        .select()
        .single();

      if (error) throw error;
      
      // Refresh appointments list
      await fetchAppointments();
      return data;
    } catch (error) {
      console.error('Error creating appointment:', error);
      throw error;
    }
  };

  const updateAppointment = async (id: string, updates: Partial<Appointment>) => {
    try {
      const { data, error } = await supabase
        .from('appointments')
        .update({
          ...updates,
          sync_status: 'pending' // Mark for sync when updated
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      
      // Refresh appointments list
      await fetchAppointments();
      return data;
    } catch (error) {
      console.error('Error updating appointment:', error);
      throw error;
    }
  };

  const deleteAppointment = async (id: string) => {
    try {
      const { error } = await supabase
        .from('appointments')
        .delete()
        .eq('id', id);

      if (error) throw error;
      
      // Refresh appointments list
      await fetchAppointments();
    } catch (error) {
      console.error('Error deleting appointment:', error);
      throw error;
    }
  };

  useEffect(() => {
    fetchAppointments();
  }, [user, currentCompany?.id]);

  return {
    appointments,
    loading,
    createAppointment,
    updateAppointment,
    deleteAppointment,
    refreshAppointments: fetchAppointments
  };
};