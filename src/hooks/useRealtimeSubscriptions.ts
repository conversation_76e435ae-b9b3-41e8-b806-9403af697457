import { useEffect, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useCompany } from '@/contexts/CompanyContext';

type SubscriptionCallback = (payload: any) => void;

interface SubscriptionConfig {
  table: string;
  event: string;
  callback: SubscriptionCallback;
}

// Global subscription manager to prevent duplicates
class RealtimeSubscriptionManager {
  private static instance: RealtimeSubscriptionManager;
  private subscriptions = new Map<string, any>();
  private callbacks = new Map<string, Set<SubscriptionCallback>>();

  static getInstance(): RealtimeSubscriptionManager {
    if (!RealtimeSubscriptionManager.instance) {
      RealtimeSubscriptionManager.instance = new RealtimeSubscriptionManager();
    }
    return RealtimeSubscriptionManager.instance;
  }

  subscribe(companyId: string, config: SubscriptionConfig): () => void {
    const key = `${companyId}-${config.table}-${config.event}`;
    
    // Add callback to the set
    if (!this.callbacks.has(key)) {
      this.callbacks.set(key, new Set());
    }
    this.callbacks.get(key)!.add(config.callback);

    // Create subscription if it doesn't exist
    if (!this.subscriptions.has(key)) {
      console.log(`🔗 Creating real-time subscription for ${config.table} (${config.event}) - Total callbacks: ${this.callbacks.get(key)?.size || 0}`);

      const channel = supabase
        .channel(`${config.table}-${config.event}-${companyId}`)
        .on(
          'postgres_changes',
          {
            event: config.event as any,
            schema: 'public',
            table: config.table,
            filter: config.table === 'leads' || config.table === 'lead_answer_status'
              ? `company_id=eq.${companyId}`
              : undefined
          },
          (payload) => {
            const callbacks = this.callbacks.get(key);
            console.log(`📡 ${config.table} change received - Broadcasting to ${callbacks?.size || 0} callbacks:`, payload);
            // Call all registered callbacks
            if (callbacks) {
              callbacks.forEach(callback => callback(payload));
            }
          }
        )
        .subscribe();

      this.subscriptions.set(key, channel);
    } else {
      console.log(`♻️ Reusing existing subscription for ${config.table} (${config.event}) - Total callbacks: ${this.callbacks.get(key)?.size || 0}`);
    }

    // Return unsubscribe function
    return () => {
      const callbacks = this.callbacks.get(key);
      if (callbacks) {
        callbacks.delete(config.callback);
        
        // If no more callbacks, remove the subscription
        if (callbacks.size === 0) {
          console.log(`🔌 Removing real-time subscription for ${config.table} (${config.event})`);
          const channel = this.subscriptions.get(key);
          if (channel) {
            supabase.removeChannel(channel);
            this.subscriptions.delete(key);
          }
          this.callbacks.delete(key);
        }
      }
    };
  }

  // Clean up all subscriptions for a company
  cleanupCompany(companyId: string) {
    const keysToRemove: string[] = [];
    
    this.subscriptions.forEach((channel, key) => {
      if (key.startsWith(companyId)) {
        console.log(`🧹 Cleaning up subscription: ${key}`);
        supabase.removeChannel(channel);
        keysToRemove.push(key);
      }
    });

    keysToRemove.forEach(key => {
      this.subscriptions.delete(key);
      this.callbacks.delete(key);
    });
  }
}

// Hook for managing real-time subscriptions
export const useRealtimeSubscriptions = () => {
  const { currentCompany } = useCompany();
  const manager = useRef(RealtimeSubscriptionManager.getInstance());
  const unsubscribeFunctions = useRef<(() => void)[]>([]);

  // Subscribe to a table
  const subscribe = (table: string, event: string, callback: SubscriptionCallback) => {
    if (!currentCompany?.id) return () => {};

    const unsubscribe = manager.current.subscribe(currentCompany.id, {
      table,
      event,
      callback
    });

    unsubscribeFunctions.current.push(unsubscribe);
    return unsubscribe;
  };

  // Cleanup on company change or unmount
  useEffect(() => {
    return () => {
      // Cleanup all subscriptions when component unmounts or company changes
      unsubscribeFunctions.current.forEach(unsubscribe => unsubscribe());
      unsubscribeFunctions.current = [];
    };
  }, [currentCompany?.id]);

  return { subscribe };
};

// Convenience hooks for specific tables
export const useLeadsRealtimeSubscription = (callback: SubscriptionCallback) => {
  const { subscribe } = useRealtimeSubscriptions();
  
  useEffect(() => {
    const unsubscribe = subscribe('leads', '*', callback);
    return unsubscribe;
  }, [callback, subscribe]);
};

export const useLeadAnswerStatusRealtimeSubscription = (callback: SubscriptionCallback) => {
  const { subscribe } = useRealtimeSubscriptions();
  
  useEffect(() => {
    const unsubscribe = subscribe('lead_answer_status', '*', callback);
    return unsubscribe;
  }, [callback, subscribe]);
};

export const useWhatsAppRealtimeSubscriptions = (
  conversationCallback: SubscriptionCallback,
  messageCallback: SubscriptionCallback
) => {
  const { subscribe } = useRealtimeSubscriptions();
  
  useEffect(() => {
    const unsubscribeConversations = subscribe('whatsapp_conversations', '*', conversationCallback);
    const unsubscribeMessages = subscribe('whatsapp_messages', '*', messageCallback);
    
    return () => {
      unsubscribeConversations();
      unsubscribeMessages();
    };
  }, [conversationCallback, messageCallback, subscribe]);
};
