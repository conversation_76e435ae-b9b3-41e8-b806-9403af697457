import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';

interface CalendarConnection {
  id: string;
  provider: string;
  sync_enabled: boolean;
  last_sync_at: string | null;
  connection_status: string;
  created_at: string;
}

export const useCalendarConnections = () => {
  const [connections, setConnections] = useState<CalendarConnection[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  const fetchConnections = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('calendar_connections')
        .select('*')
        .eq('user_id', user.id);

      if (error) throw error;
      setConnections(data || []);
    } catch (error) {
      console.error('Error fetching calendar connections:', error);
    } finally {
      setLoading(false);
    }
  };

  const connectProvider = async (provider: 'google' | 'outlook') => {
    if (!user) throw new Error('User not authenticated');

    // For now, this will be a placeholder until we implement OAuth
    // In a real implementation, this would redirect to OAuth flow
    console.log(`Connecting to ${provider} calendar...`);
    
    // This is a mock implementation - in reality we'd:
    // 1. Redirect to OAuth provider
    // 2. Get authorization code
    // 3. Exchange for access/refresh tokens
    // 4. Store tokens in database
    
    throw new Error('OAuth integration not yet implemented. This requires setting up Google/Microsoft app credentials.');
  };

  const disconnectProvider = async (connectionId: string) => {
    try {
      const { error } = await supabase
        .from('calendar_connections')
        .delete()
        .eq('id', connectionId);

      if (error) throw error;
      
      // Refresh connections list
      await fetchConnections();
    } catch (error) {
      console.error('Error disconnecting provider:', error);
      throw error;
    }
  };

  const syncCalendar = async (connectionId: string) => {
    try {
      // This would call the calendar-sync edge function
      const { data, error } = await supabase.functions.invoke('calendar-sync', {
        body: { connectionId }
      });

      if (error) throw error;

      // Update last sync time
      const { error: updateError } = await supabase
        .from('calendar_connections')
        .update({ last_sync_at: new Date().toISOString() })
        .eq('id', connectionId);

      if (updateError) throw updateError;

      // Refresh connections list
      await fetchConnections();
    } catch (error) {
      console.error('Error syncing calendar:', error);
      throw error;
    }
  };

  useEffect(() => {
    fetchConnections();
  }, [user]);

  return {
    connections,
    loading,
    connectProvider,
    disconnectProvider,
    syncCalendar,
    refreshConnections: fetchConnections
  };
};