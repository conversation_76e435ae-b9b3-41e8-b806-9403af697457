import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, Settings, Zap, AlertCircle } from "lucide-react";
import { useCalendarConnections } from "@/hooks/useCalendarConnections";
import { useToast } from "@/hooks/use-toast";
import { useState } from "react";
import { UserManagement } from "@/components/settings/UserManagement";

const SettingsPage = () => {
  const { connections, loading, connectProvider, disconnectProvider, syncCalendar } = useCalendarConnections();
  const { toast } = useToast();
  const [syncing, setSyncing] = useState<string | null>(null);

  const handleConnect = async (provider: 'google' | 'outlook') => {
    try {
      await connectProvider(provider);
      toast({
        title: "הצלחה",
        description: `חיבור ל-${provider === 'google' ? 'Google Calendar' : 'Outlook Calendar'} בוצע בהצלחה`,
      });
    } catch (error) {
      toast({
        title: "שגיאה",
        description: "נכשל בחיבור ליומן",
        variant: "destructive",
      });
    }
  };

  const handleDisconnect = async (connectionId: string) => {
    try {
      await disconnectProvider(connectionId);
      toast({
        title: "הצלחה",
        description: "החיבור ליומן נותק בהצלחה",
      });
    } catch (error) {
      toast({
        title: "שגיאה",
        description: "נכשל בניתוק החיבור",
        variant: "destructive",
      });
    }
  };

  const handleSync = async (connectionId: string) => {
    setSyncing(connectionId);
    try {
      await syncCalendar(connectionId);
      toast({
        title: "הצלחה",
        description: "הסינכרון הושלם בהצלחה",
      });
    } catch (error) {
      toast({
        title: "שגיאה",
        description: "נכשל בסינכרון היומן",
        variant: "destructive",
      });
    } finally {
      setSyncing(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-success text-success-foreground">פעיל</Badge>;
      case 'error':
        return <Badge variant="destructive">שגיאה</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground mb-2 flex items-center gap-2" dir="rtl">
          <Settings className="h-8 w-8" />
          הגדרות
        </h1>
        <p className="text-muted-foreground" dir="rtl">ניהול חיבורי יומן והגדרות אישיות</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2" dir="rtl">
            <Calendar className="h-5 w-5" />
            חיבורי יומן
          </CardTitle>
          <CardDescription dir="rtl">
            חבר את היומנים שלך לסינכרון דו-כיווני
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Google Calendar */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <Calendar className="h-5 w-5 text-blue-600" />
              </div>
              <div dir="rtl">
                <h3 className="font-medium">Google Calendar</h3>
                <p className="text-sm text-muted-foreground">סינכרון עם יומן גוגל</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {(() => {
                const googleConnection = connections.find(c => c.provider === 'google');
                if (googleConnection) {
                  return (
                    <>
                      {getStatusBadge(googleConnection.connection_status)}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSync(googleConnection.id)}
                        disabled={syncing === googleConnection.id}
                      >
                        {syncing === googleConnection.id ? (
                          <Zap className="h-4 w-4 animate-spin" />
                        ) : (
                          <Zap className="h-4 w-4" />
                        )}
                        סנכרן
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDisconnect(googleConnection.id)}
                      >
                        נתק
                      </Button>
                    </>
                  );
                } else {
                  return (
                    <Button
                      onClick={() => handleConnect('google')}
                      disabled={loading}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      חבר
                    </Button>
                  );
                }
              })()}
            </div>
          </div>

          {/* Outlook Calendar */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <Calendar className="h-5 w-5 text-blue-800" />
              </div>
              <div dir="rtl">
                <h3 className="font-medium">Outlook Calendar</h3>
                <p className="text-sm text-muted-foreground">סינכרון עם יומן אאוטלוק</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {(() => {
                const outlookConnection = connections.find(c => c.provider === 'outlook');
                if (outlookConnection) {
                  return (
                    <>
                      {getStatusBadge(outlookConnection.connection_status)}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSync(outlookConnection.id)}
                        disabled={syncing === outlookConnection.id}
                      >
                        {syncing === outlookConnection.id ? (
                          <Zap className="h-4 w-4 animate-spin" />
                        ) : (
                          <Zap className="h-4 w-4" />
                        )}
                        סנכרן
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDisconnect(outlookConnection.id)}
                      >
                        נתק
                      </Button>
                    </>
                  );
                } else {
                  return (
                    <Button
                      onClick={() => handleConnect('outlook')}
                      disabled={loading}
                      className="bg-blue-800 hover:bg-blue-900"
                    >
                      חבר
                    </Button>
                  );
                }
              })()}
            </div>
          </div>

          {connections.length > 0 && (
            <div className="mt-6">
              <h4 className="font-medium mb-3" dir="rtl">פרטי חיבורים</h4>
              <div className="space-y-2">
                {connections.map((connection) => (
                  <div key={connection.id} className="text-sm text-muted-foreground p-3 bg-muted rounded-lg" dir="rtl">
                    <div className="flex justify-between items-center">
                      <span>יומן {connection.provider === 'google' ? 'גוגל' : 'אאוטלוק'}</span>
                      <span>
                        סינכרון אחרון: {connection.last_sync_at 
                          ? new Date(connection.last_sync_at).toLocaleString('he-IL') 
                          : 'לא סונכרן עדיין'
                        }
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {connections.length === 0 && !loading && (
            <div className="text-center py-6 text-muted-foreground" dir="rtl">
              <AlertCircle className="h-8 w-8 mx-auto mb-2" />
              <p>לא קיימים חיבורי יומן</p>
              <p className="text-sm">חבר יומן כדי להתחיל בסינכרון</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* User Management Section */}
      <UserManagement />
    </div>
  );
};

export default SettingsPage;