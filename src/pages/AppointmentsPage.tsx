import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Plus, Calendar, Clock, User, Eye, Edit, Loader2 } from "lucide-react";
import { AppointmentModal } from "@/components/office/AppointmentModal";
import { useAppointments } from "@/hooks/useAppointments";

const AppointmentsPage = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingAppointment, setEditingAppointment] = useState(null);
  const { appointments, loading } = useAppointments();

  const getStatusColor = (status: string) => {
    switch (status) {
      case "חדשה":
        return "bg-primary/10 text-primary border-primary/20";
      case "הופיע":
        return "bg-success/10 text-success border-success/20";
      case "לא הופיע":
        return "bg-warning/10 text-warning border-warning/20";
      case "בוטלה":
        return "bg-muted text-muted-foreground border-border";
      default:
        return "bg-muted text-muted-foreground border-border";
    }
  };

  const handleEdit = (appointment: any) => {
    setEditingAppointment(appointment);
    setIsModalOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground mb-2">פגישות</h1>
          <p className="text-muted-foreground">ניהול פגישות ולוח זמנים</p>
        </div>
        <Button 
          className="btn-professional flex items-center gap-2"
          onClick={() => setIsModalOpen(true)}
        >
          <Plus className="w-4 h-4" />
          קבע פגישה חדשה
        </Button>
      </div>

      <Card className="card-professional">
        <CardHeader>
          <CardTitle>רשימת פגישות</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="professional-table">
              <thead>
                <tr>
                  <th>פעולות</th>
                  <th>סוג פגישה</th>
                  <th>סטטוס</th>
                  <th>תאריך פגישה</th>
                  <th>תאריך יצירה</th>
                  <th>לקוח</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={6} className="text-center py-8">
                      <Loader2 className="w-6 h-6 animate-spin mx-auto" />
                    </td>
                  </tr>
                ) : appointments.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="text-center py-8 text-muted-foreground">
                      אין פגישות
                    </td>
                  </tr>
                ) : appointments.map((appointment) => (
                  <tr key={appointment.id}>
                    <td>
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0"
                          onClick={() => handleEdit(appointment)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                      </div>
                    </td>
                    <td>{appointment.title}</td>
                    <td>
                      <span className={`px-2 py-1 rounded-md text-xs border ${getStatusColor("חדשה")}`}>
                        מתוכנן
                      </span>
                    </td>
                    <td>
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-primary" />
                        {new Date(appointment.start_time).toLocaleString('he-IL')}
                      </div>
                    </td>
                    <td>
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-primary" />
                        {new Date(appointment.created_at).toLocaleDateString('he-IL')}
                      </div>
                    </td>
                    <td>
                      <div className="flex items-center gap-2">
                        <User className="w-4 h-4 text-primary" />
                        לא צוין
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <AppointmentModal 
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingAppointment(null);
        }}
        editingAppointment={editingAppointment}
      />
    </div>
  );
};

export default AppointmentsPage;