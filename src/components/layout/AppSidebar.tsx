import { NavLink, useLocation } from "react-router-dom";
import { Building2, FolderOpen, Scale, Users, Calendar, MessageSquare, FileText, Clock, CheckCircle, Shield, Settings, BarChart3, Crown, Workflow } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

const navigationItems = [
  {
    title: "דשבורד",
    icon: BarChart3,
    items: [
      {
        title: "דשבורד ראשי",
        url: "/dashboard",
        icon: BarChart3,
      },
    ]
  },
  {
    title: "משרד",
    icon: Building2,
    items: [
      {
        title: "לידים",
        url: "/office/leads",
        icon: Users,
      },
      {
        title: "פגישות",
        url: "/office/appointments",
        icon: Calendar,
      },
      {
        title: "וואטסאפ",
        url: "/office/whatsapp",
        icon: MessageSquare,
      },
      {
        title: "אוטומציה שיווקית",
        url: "/office/marketing-automation",
        icon: Workflow,
      },

    ]
  },
  {
    title: "ניהול תיקים",
    icon: FolderOpen,
    items: [
      {
        title: "תיקים",
        url: "/cases",
        icon: FolderOpen,
      },
    ]
  },
  {
    title: "ניהול",
    icon: Shield,
    items: [
      {
        title: "הגדרות",
        url: "/settings",
        icon: Settings,
      },
      {
        title: "הגדרות חברה",
        url: "/company-settings",
        icon: Building2,
      },
    ]
  },
  {
    title: "Super Admin",
    icon: Crown,
    items: [
      {
        title: "ניהול מערכת",
        url: "/super-admin",
        icon: Crown,
      },
    ]
  },
];

export function AppSidebar() {
  const location = useLocation();
  const currentPath = location.pathname;
  const { isSuperAdmin, isCompanyAdmin } = useAuth();

  const isActive = (path: string) => 
    currentPath === path || (path !== "/" && currentPath.startsWith(path));

  const isGroupActive = (group: any) =>
    group.items.some((item: any) => isActive(item.url));

  const { open } = useSidebar();

  return (
    <Sidebar
      side="right"
      collapsible="icon"
    >
      <SidebarContent>
        {/* Logo Section */}
        <div className="p-4 border-b border-sidebar-border">
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-10 h-10 bg-gradient-professional rounded-lg">
              <Scale className="w-6 h-6 text-primary-foreground" />
            </div>
            {open && (
              <div>
                <h1 className="text-lg font-bold text-sidebar-foreground">ITPS</h1>
                <p className="text-xs text-sidebar-foreground">מערכת ניהול משרד</p>
              </div>
            )}
          </div>
        </div>

        <SidebarGroup>
          <SidebarGroupLabel className="text-sidebar-foreground font-medium">
            ניהול ראשי
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.filter(group => {
                // Show Super Admin group only for super admins
                if (group.title === "Super Admin") {
                  return isSuperAdmin;
                }
                // Show Management group for both super admins and company admins
                if (group.title === "ניהול") {
                  return isSuperAdmin || isCompanyAdmin;
                }
                return true;
              }).map((group) => (
                <Collapsible
                  key={group.title}
                  defaultOpen={isGroupActive(group)}
                  className="group/collapsible"
                >
                  <SidebarMenuItem>
                    <CollapsibleTrigger asChild>
                      <SidebarMenuButton className="text-base font-semibold">
                        <group.icon className="w-5 h-5 flex-shrink-0" />
                        {open && <span>{group.title}</span>}
                      </SidebarMenuButton>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <SidebarMenuSub>
                        {group.items.filter(subItem => {
                          // Filter items within the Management group based on user role
                          if (group.title === "ניהול") {
                            // Company admins can only see Settings, not Company Settings
                            if (subItem.title === "הגדרות חברה") {
                              return isSuperAdmin;
                            }
                          }
                          return true;
                        }).map((subItem) => (
                          <SidebarMenuSubItem key={subItem.title}>
                            <SidebarMenuSubButton asChild>
                              <NavLink
                                to={subItem.url}
                                className={({ isActive: linkIsActive }) =>
                                  `flex items-center gap-3 px-3 py-1.5 rounded-md transition-colors text-sm ${
                                    linkIsActive || isActive(subItem.url)
                                      ? "bg-sidebar-accent text-sidebar-accent-foreground font-medium"
                                      : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                                  }`
                                }
                              >
                                <subItem.icon className="w-4 h-4 flex-shrink-0" />
                                {open && <span>{subItem.title}</span>}
                              </NavLink>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        ))}
                      </SidebarMenuSub>
                    </CollapsibleContent>
                  </SidebarMenuItem>
                </Collapsible>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}