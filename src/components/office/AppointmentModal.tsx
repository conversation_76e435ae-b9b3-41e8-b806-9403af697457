import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { useAppointments } from "@/hooks/useAppointments";

interface AppointmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  editingAppointment?: any;
}

export const AppointmentModal = ({ isOpen, onClose, editingAppointment }: AppointmentModalProps) => {
  const { toast } = useToast();
  const { createAppointment, updateAppointment } = useAppointments();
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    startDate: "",
    startTime: "",
    endDate: "",
    endTime: "",
    location: "",
  });

  useEffect(() => {
    if (editingAppointment) {
      const startDate = new Date(editingAppointment.start_time);
      const endDate = new Date(editingAppointment.end_time);
      
      setFormData({
        title: editingAppointment.title || "",
        description: editingAppointment.description || "",
        startDate: startDate.toISOString().split('T')[0],
        startTime: startDate.toTimeString().slice(0, 5),
        endDate: endDate.toISOString().split('T')[0],
        endTime: endDate.toTimeString().slice(0, 5),
        location: editingAppointment.location || "",
      });
    } else {
      setFormData({
        title: "",
        description: "",
        startDate: "",
        startTime: "",
        endDate: "",
        endTime: "",
        location: "",
      });
    }
  }, [editingAppointment, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.startDate || !formData.startTime) {
      toast({
        title: "שגיאה",
        description: "יש למלא את כל השדות הנדרשים",
        variant: "destructive",
      });
      return;
    }

    try {
      const startDateTime = new Date(`${formData.startDate}T${formData.startTime}`);
      const endDateTime = formData.endDate && formData.endTime 
        ? new Date(`${formData.endDate}T${formData.endTime}`)
        : new Date(startDateTime.getTime() + 60 * 60 * 1000); // Default to 1 hour later

      const appointmentData = {
        title: formData.title,
        description: formData.description,
        start_time: startDateTime.toISOString(),
        end_time: endDateTime.toISOString(),
        location: formData.location,
        lead_id: null,
        external_event_id: null,
        calendar_provider: null,
      };

      if (editingAppointment) {
        await updateAppointment(editingAppointment.id, appointmentData);
        toast({
          title: "הפגישה עודכנה בהצלחה",
          description: "הפגישה עודכנה במערכת",
        });
      } else {
        await createAppointment(appointmentData);
        toast({
          title: "הפגישה נוצרה בהצלחה",
          description: "הפגישה נוספה למערכת",
        });
      }
      
      onClose();
    } catch (error) {
      toast({
        title: "שגיאה",
        description: "נכשל בשמירת הפגישה",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]" dir="rtl">
        <DialogHeader>
          <DialogTitle>
            {editingAppointment ? "עריכת פגישה" : "פגישה חדשה"}
          </DialogTitle>
          <DialogDescription>
            {editingAppointment ? "ערוך את פרטי הפגישה" : "הוסף פגישה חדשה ללוח הזמנים"}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <Label htmlFor="title">כותרת הפגישה *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                placeholder="הזן כותרת לפגישה"
                required
              />
            </div>

            <div>
              <Label htmlFor="description">תיאור</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="הזן תיאור לפגישה"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate">תאריך התחלה *</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                  required
                />
              </div>
              <div>
                <Label htmlFor="startTime">שעת התחלה *</Label>
                <Input
                  id="startTime"
                  type="time"
                  value={formData.startTime}
                  onChange={(e) => setFormData({ ...formData, startTime: e.target.value })}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="endDate">תאריך סיום</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="endTime">שעת סיום</Label>
                <Input
                  id="endTime"
                  type="time"
                  value={formData.endTime}
                  onChange={(e) => setFormData({ ...formData, endTime: e.target.value })}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="location">מיקום</Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                placeholder="הזן מיקום הפגישה"
              />
            </div>
          </div>

          <div className="flex justify-end gap-3 mt-6">
            <Button type="button" variant="outline" onClick={onClose}>
              ביטול
            </Button>
            <Button type="submit">
              {editingAppointment ? "עדכן" : "צור פגישה"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};