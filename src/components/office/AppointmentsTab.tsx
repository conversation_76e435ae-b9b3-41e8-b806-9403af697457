import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Plus, Calendar, Clock, User, Eye, Edit, Loader2 } from "lucide-react";
import { AppointmentModal } from "./AppointmentModal";
import { useAppointments } from "@/hooks/useAppointments";
import { useToast } from "@/hooks/use-toast";

export const AppointmentsTab = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingAppointment, setEditingAppointment] = useState(null);
  const { appointments, loading, deleteAppointment } = useAppointments();
  const { toast } = useToast();

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-warning/10 text-warning border-warning/20";
      case "synced":
        return "bg-success/10 text-success border-success/20";
      case "failed":
        return "bg-destructive/10 text-destructive border-destructive/20";
      default:
        return "bg-muted text-muted-foreground border-border";
    }
  };

  const handleEdit = (appointment: any) => {
    setEditingAppointment(appointment);
    setIsModalOpen(true);
  };

  const handleDelete = async (appointmentId: string) => {
    try {
      await deleteAppointment(appointmentId);
      toast({
        title: "הצלחה",
        description: "הפגישה נמחקה בהצלחה",
      });
    } catch (error) {
      toast({
        title: "שגיאה",
        description: "נכשל במחיקת הפגישה",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-foreground">פגישות</h2>
          <p className="text-foreground text-sm">ניהול פגישות ולוח זמנים</p>
        </div>
        <Button 
          className="btn-professional flex items-center gap-2"
          onClick={() => setIsModalOpen(true)}
        >
          <Plus className="w-4 h-4" />
          קבע פגישה חדשה
        </Button>
      </div>

      <Card className="card-professional">
        <CardHeader>
          <CardTitle>רשימת פגישות</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="professional-table">
              <thead>
                <tr>
                  <th>פעולות</th>
                  <th>כותרת</th>
                  <th>סטטוס סינכרון</th>
                  <th>תאריך תחילה</th>
                  <th>תאריך יצירה</th>
                  <th>תיאור</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={6} className="text-center py-8">
                      <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                      <p className="mt-2 text-muted-foreground">טוען פגישות...</p>
                    </td>
                  </tr>
                ) : appointments.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="text-center py-8 text-muted-foreground">
                      אין פגישות להצגה
                    </td>
                  </tr>
                ) : (
                  appointments.map((appointment) => (
                    <tr key={appointment.id}>
                      <td>
                        <div className="flex items-center gap-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            onClick={() => handleEdit(appointment)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            onClick={() => handleDelete(appointment.id)}
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                      <td>{appointment.title}</td>
                      <td>
                        <span className={`px-2 py-1 rounded-md text-xs border ${getStatusColor(appointment.sync_status)}`}>
                          {appointment.sync_status === 'pending' ? 'ממתין לסינכרון' :
                           appointment.sync_status === 'synced' ? 'מסונכרן' :
                           appointment.sync_status === 'failed' ? 'נכשל בסינכרון' : appointment.sync_status}
                        </span>
                      </td>
                      <td>
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4 text-primary" />
                          {new Date(appointment.start_time).toLocaleString('he-IL')}
                        </div>
                      </td>
                      <td>
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4 text-primary" />
                          {new Date(appointment.created_at).toLocaleDateString('he-IL')}
                        </div>
                      </td>
                      <td>
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4 text-primary" />
                          {appointment.description || 'ללא תיאור'}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <AppointmentModal 
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingAppointment(null);
        }}
        editingAppointment={editingAppointment}
      />
    </div>
  );
};