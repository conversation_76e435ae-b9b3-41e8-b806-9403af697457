-- Multi-Tenant Database Schema
-- This migration sets up proper multi-tenancy with companies, user roles, and RLS

-- First, let's update the companies table to include all necessary fields
ALTER TABLE public.companies 
ADD COLUMN IF NOT EXISTS status text DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'inactive')),
ADD COLUMN IF NOT EXISTS subscription_plan text DEFAULT 'basic',
ADD COLUMN IF NOT EXISTS max_users integer DEFAULT 10,
ADD COLUMN IF NOT EXISTS settings jsonb DEFAULT '{}',
ADD COLUMN IF NOT EXISTS api_tokens jsonb DEFAULT '{}', -- Store encrypted API tokens
ADD COLUMN IF NOT EXISTS onboarded_at timestamp with time zone,
ADD COLUMN IF NOT EXISTS onboarded_by uuid REFERENCES auth.users(id);

-- Update user_roles table to support different role types
ALTER TABLE public.user_roles 
DROP CONSTRAINT IF EXISTS user_roles_role_check;

ALTER TABLE public.user_roles 
ADD CONSTRAINT user_roles_role_check 
CHECK (role IN ('super_admin', 'company_admin', 'company_user'));

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_companies_status ON public.companies(status);
CREATE INDEX IF NOT EXISTS idx_user_roles_company_role ON public.user_roles(company_id, role);
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON public.user_roles(user_id);

-- Create a function to check if user is super admin
CREATE OR REPLACE FUNCTION public.is_super_admin(user_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.user_roles 
    WHERE user_roles.user_id = $1 AND role = 'super_admin'
  );
$$;

-- Create a function to get user's company_id
CREATE OR REPLACE FUNCTION public.get_user_company_id(user_id uuid)
RETURNS uuid
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT company_id FROM public.user_roles 
  WHERE user_roles.user_id = $1 
  LIMIT 1;
$$;

-- Create a function to check if user is company admin
CREATE OR REPLACE FUNCTION public.is_company_admin(user_id uuid, target_company_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.user_roles 
    WHERE user_roles.user_id = $1 
    AND company_id = $2 
    AND role = 'company_admin'
  );
$$;

-- Update RLS policies for companies table
DROP POLICY IF EXISTS "Companies are viewable by company members" ON public.companies;
DROP POLICY IF EXISTS "Companies are editable by company admins" ON public.companies;
DROP POLICY IF EXISTS "Super admins can view all companies" ON public.companies;
DROP POLICY IF EXISTS "Super admins can manage all companies" ON public.companies;

-- Super admins can see and manage all companies
CREATE POLICY "Super admins can view all companies" ON public.companies
  FOR SELECT USING (public.is_super_admin(auth.uid()));

CREATE POLICY "Super admins can manage all companies" ON public.companies
  FOR ALL USING (public.is_super_admin(auth.uid()));

-- Company members can view their own company
CREATE POLICY "Company members can view their company" ON public.companies
  FOR SELECT USING (
    id = public.get_user_company_id(auth.uid())
  );

-- Company admins can update their own company
CREATE POLICY "Company admins can update their company" ON public.companies
  FOR UPDATE USING (
    public.is_company_admin(auth.uid(), id)
  );

-- Update RLS policies for user_roles table
DROP POLICY IF EXISTS "User roles are viewable by company members" ON public.user_roles;
DROP POLICY IF EXISTS "User roles are manageable by company admins" ON public.user_roles;
DROP POLICY IF EXISTS "Super admins can manage all user roles" ON public.user_roles;

-- Super admins can manage all user roles
CREATE POLICY "Super admins can manage all user roles" ON public.user_roles
  FOR ALL USING (public.is_super_admin(auth.uid()));

-- Company admins can view and manage user roles in their company
CREATE POLICY "Company admins can manage company user roles" ON public.user_roles
  FOR ALL USING (
    public.is_company_admin(auth.uid(), company_id)
  );

-- Users can view their own role
CREATE POLICY "Users can view their own role" ON public.user_roles
  FOR SELECT USING (user_id = auth.uid());

-- Update RLS policies for leads table
DROP POLICY IF EXISTS "Leads are viewable by company members" ON public.leads;
DROP POLICY IF EXISTS "Leads are manageable by company members" ON public.leads;

CREATE POLICY "Company members can manage their company leads" ON public.leads
  FOR ALL USING (
    company_id = public.get_user_company_id(auth.uid())
  );

-- Update RLS policies for cases table
DROP POLICY IF EXISTS "Cases are viewable by company members" ON public.cases;
DROP POLICY IF EXISTS "Cases are manageable by company members" ON public.cases;

CREATE POLICY "Company members can manage their company cases" ON public.cases
  FOR ALL USING (
    company_id = public.get_user_company_id(auth.uid())
  );

-- Update RLS policies for case_types table
DROP POLICY IF EXISTS "Case types are viewable by company members" ON public.case_types;
DROP POLICY IF EXISTS "Case types are manageable by company members" ON public.case_types;

CREATE POLICY "Company members can manage their company case types" ON public.case_types
  FOR ALL USING (
    company_id = public.get_user_company_id(auth.uid())
  );

-- Update RLS policies for case_time_entries table
DROP POLICY IF EXISTS "Time entries are viewable by company members" ON public.case_time_entries;
DROP POLICY IF EXISTS "Time entries are manageable by company members" ON public.case_time_entries;

CREATE POLICY "Company members can manage their company time entries" ON public.case_time_entries
  FOR ALL USING (
    company_id = public.get_user_company_id(auth.uid())
  );

-- Update RLS policies for appointments table
DROP POLICY IF EXISTS "Appointments are viewable by company members" ON public.appointments;
DROP POLICY IF EXISTS "Appointments are manageable by company members" ON public.appointments;

CREATE POLICY "Company members can manage their company appointments" ON public.appointments
  FOR ALL USING (
    company_id = public.get_user_company_id(auth.uid())
  );

-- Update RLS policies for whatsapp_conversations table
DROP POLICY IF EXISTS "WhatsApp conversations are viewable by company members" ON public.whatsapp_conversations;
DROP POLICY IF EXISTS "WhatsApp conversations are manageable by company members" ON public.whatsapp_conversations;

CREATE POLICY "Company members can manage their company WhatsApp conversations" ON public.whatsapp_conversations
  FOR ALL USING (
    company_id = public.get_user_company_id(auth.uid())
  );

-- Update RLS policies for whatsapp_messages table
DROP POLICY IF EXISTS "WhatsApp messages are viewable by company members" ON public.whatsapp_messages;
DROP POLICY IF EXISTS "WhatsApp messages are manageable by company members" ON public.whatsapp_messages;

CREATE POLICY "Company members can manage their company WhatsApp messages" ON public.whatsapp_messages
  FOR ALL USING (
    company_id = public.get_user_company_id(auth.uid())
  );

-- Update RLS policies for lead_activities table
DROP POLICY IF EXISTS "Lead activities are viewable by company members" ON public.lead_activities;
DROP POLICY IF EXISTS "Lead activities are manageable by company members" ON public.lead_activities;

CREATE POLICY "Company members can manage their company lead activities" ON public.lead_activities
  FOR ALL USING (
    company_id = public.get_user_company_id(auth.uid())
  );

-- Create a trigger to automatically set company_id for new records
CREATE OR REPLACE FUNCTION public.set_company_id()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only set company_id if it's not already set and user is not super admin
  IF NEW.company_id IS NULL AND NOT public.is_super_admin(auth.uid()) THEN
    NEW.company_id := public.get_user_company_id(auth.uid());
  END IF;
  RETURN NEW;
END;
$$;

-- Apply the trigger to relevant tables
DROP TRIGGER IF EXISTS set_company_id_trigger ON public.leads;
CREATE TRIGGER set_company_id_trigger
  BEFORE INSERT ON public.leads
  FOR EACH ROW EXECUTE FUNCTION public.set_company_id();

DROP TRIGGER IF EXISTS set_company_id_trigger ON public.cases;
CREATE TRIGGER set_company_id_trigger
  BEFORE INSERT ON public.cases
  FOR EACH ROW EXECUTE FUNCTION public.set_company_id();

DROP TRIGGER IF EXISTS set_company_id_trigger ON public.case_types;
CREATE TRIGGER set_company_id_trigger
  BEFORE INSERT ON public.case_types
  FOR EACH ROW EXECUTE FUNCTION public.set_company_id();

DROP TRIGGER IF EXISTS set_company_id_trigger ON public.case_time_entries;
CREATE TRIGGER set_company_id_trigger
  BEFORE INSERT ON public.case_time_entries
  FOR EACH ROW EXECUTE FUNCTION public.set_company_id();

DROP TRIGGER IF EXISTS set_company_id_trigger ON public.appointments;
CREATE TRIGGER set_company_id_trigger
  BEFORE INSERT ON public.appointments
  FOR EACH ROW EXECUTE FUNCTION public.set_company_id();

DROP TRIGGER IF EXISTS set_company_id_trigger ON public.whatsapp_conversations;
CREATE TRIGGER set_company_id_trigger
  BEFORE INSERT ON public.whatsapp_conversations
  FOR EACH ROW EXECUTE FUNCTION public.set_company_id();

DROP TRIGGER IF EXISTS set_company_id_trigger ON public.whatsapp_messages;
CREATE TRIGGER set_company_id_trigger
  BEFORE INSERT ON public.whatsapp_messages
  FOR EACH ROW EXECUTE FUNCTION public.set_company_id();

DROP TRIGGER IF EXISTS set_company_id_trigger ON public.lead_activities;
CREATE TRIGGER set_company_id_trigger
  BEFORE INSERT ON public.lead_activities
  FOR EACH ROW EXECUTE FUNCTION public.set_company_id();
