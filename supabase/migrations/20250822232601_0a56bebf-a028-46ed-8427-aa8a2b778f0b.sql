-- Create appointments table
CREATE TABLE public.appointments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMES<PERSON>MP WITH TIME ZONE NOT NULL,
  location TEXT,
  lead_id UUID,
  user_id UUID NOT NULL,
  company_id UUID NOT NULL,
  external_event_id TEXT,
  calendar_provider TEXT,
  sync_status TEXT DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create calendar_connections table
CREATE TABLE public.calendar_connections (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  company_id UUID NOT NULL,
  provider TEXT NOT NULL CHECK (provider IN ('google', 'outlook')),
  access_token TEXT,
  refresh_token TEXT,
  provider_calendar_id TEXT,
  sync_enabled BOOLEAN NOT NULL DEFAULT true,
  last_sync_at TIMESTAMP WITH TIME ZONE,
  connection_status TEXT NOT NULL DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.calendar_connections ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for appointments
CREATE POLICY "Users can view company appointments" 
ON public.appointments 
FOR SELECT 
USING (company_id = get_user_company());

CREATE POLICY "Users can create company appointments" 
ON public.appointments 
FOR INSERT 
WITH CHECK (company_id = get_user_company() AND user_id = auth.uid());

CREATE POLICY "Users can update company appointments" 
ON public.appointments 
FOR UPDATE 
USING (company_id = get_user_company());

CREATE POLICY "Users can delete company appointments" 
ON public.appointments 
FOR DELETE 
USING (company_id = get_user_company());

-- Create RLS policies for calendar_connections
CREATE POLICY "Users can view their calendar connections" 
ON public.calendar_connections 
FOR SELECT 
USING (user_id = auth.uid());

CREATE POLICY "Users can create their calendar connections" 
ON public.calendar_connections 
FOR INSERT 
WITH CHECK (user_id = auth.uid() AND company_id = get_user_company());

CREATE POLICY "Users can update their calendar connections" 
ON public.calendar_connections 
FOR UPDATE 
USING (user_id = auth.uid());

CREATE POLICY "Users can delete their calendar connections" 
ON public.calendar_connections 
FOR DELETE 
USING (user_id = auth.uid());

-- Add update trigger for appointments
CREATE TRIGGER update_appointments_updated_at
BEFORE UPDATE ON public.appointments
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Add update trigger for calendar_connections
CREATE TRIGGER update_calendar_connections_updated_at
BEFORE UPDATE ON public.calendar_connections
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();