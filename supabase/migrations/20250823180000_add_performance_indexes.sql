-- Add Performance Indexes for 100+ Companies Scalability
-- This migration adds critical indexes for optimal query performance

-- Drop existing indexes if they exist to recreate with better configurations
DROP INDEX IF EXISTS idx_leads_company_created_status;
DROP INDEX IF EXISTS idx_cases_company_created_status;
DROP INDEX IF EXISTS idx_time_entries_company_created_rate;
DROP INDEX IF EXISTS idx_leads_active_company;
DROP INDEX IF EXISTS idx_cases_active_company;

-- Critical composite indexes for dashboard queries
-- These indexes support the optimized dashboard function

-- Leads indexes for fast company filtering and date range queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leads_company_created_status_value 
ON public.leads(company_id, created_at DESC, status, value) 
WHERE created_at > NOW() - INTERVAL '2 years';

-- Cases indexes for fast company filtering and joins
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cases_company_created_status_type 
ON public.cases(company_id, created_at DESC, status, case_type_id) 
WHERE created_at > NOW() - INTERVAL '2 years';

-- Time entries indexes for billing and reporting
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_time_entries_company_created_billing 
ON public.case_time_entries(company_id, created_at DESC, hourly_rate, duration, total_cost) 
WHERE created_at > NOW() - INTERVAL '2 years';

-- Partial indexes for active records (most frequently queried)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leads_active_company_recent 
ON public.leads(company_id, created_at DESC, full_name) 
WHERE status != 'לקוח סגור' AND created_at > NOW() - INTERVAL '1 year';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cases_active_company_recent 
ON public.cases(company_id, created_at DESC, title) 
WHERE status != 'סגור' AND created_at > NOW() - INTERVAL '1 year';

-- Search optimization indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leads_search_company 
ON public.leads USING gin(
  to_tsvector('hebrew', coalesce(full_name, '') || ' ' || coalesce(phone, '') || ' ' || coalesce(email, ''))
) WHERE company_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cases_search_company 
ON public.cases USING gin(
  to_tsvector('hebrew', coalesce(title, '') || ' ' || coalesce(description, ''))
) WHERE company_id IS NOT NULL;

-- Phone number optimization for WhatsApp integration
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leads_phone_company 
ON public.leads(phone, company_id) 
WHERE phone IS NOT NULL AND phone != '';

-- User activity indexes for performance monitoring
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_roles_company_role_active 
ON public.user_roles(company_id, role, created_at DESC);

-- Case type joins optimization
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_case_types_company_active 
ON public.case_types(company_id, is_active, name) 
WHERE is_active = true;

-- WhatsApp conversation indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_whatsapp_conversations_company_recent 
ON public.whatsapp_conversations(company_id, last_message_timestamp DESC) 
WHERE last_message_timestamp > NOW() - INTERVAL '6 months';

-- Appointment scheduling indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_appointments_company_datetime 
ON public.appointments(company_id, start_time DESC, status) 
WHERE start_time > NOW() - INTERVAL '1 year';

-- Lead activities for timeline queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_activities_lead_created 
ON public.lead_activities(lead_id, created_at DESC, activity_type);

-- Case time entries for case detail views
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_time_entries_case_created 
ON public.case_time_entries(case_id, created_at DESC);

-- Companies index for super admin queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_companies_status_created 
ON public.companies(status, created_at DESC, subscription_plan);

-- Monthly aggregation support indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leads_monthly_company 
ON public.leads(company_id, date_trunc('month', created_at), status, value);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cases_monthly_company 
ON public.cases(company_id, date_trunc('month', created_at), status, value);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_time_entries_monthly_company 
ON public.case_time_entries(company_id, date_trunc('month', created_at), total_cost);

-- Performance monitoring indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leads_count_by_status_company 
ON public.leads(company_id, status) 
INCLUDE (value, created_at);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cases_count_by_type_company 
ON public.cases(company_id, case_type_id) 
INCLUDE (status, value, created_at);

-- Covering indexes for common dashboard queries (PostgreSQL 11+)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_leads_dashboard_covering 
ON public.leads(company_id, created_at DESC) 
INCLUDE (status, value, full_name, phone, email);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cases_dashboard_covering 
ON public.cases(company_id, created_at DESC) 
INCLUDE (status, case_type_id, value, title, lead_id);

-- Statistics and analytics indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_time_entries_analytics 
ON public.case_time_entries(company_id, user_id, created_at DESC) 
INCLUDE (duration, hourly_rate, total_cost);

-- Foreign key optimization indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cases_lead_id 
ON public.cases(lead_id) WHERE lead_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_time_entries_case_id 
ON public.case_time_entries(case_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_activities_user_id 
ON public.lead_activities(user_id);

-- Update table statistics for better query planning
ANALYZE public.leads;
ANALYZE public.cases;
ANALYZE public.case_time_entries;
ANALYZE public.companies;
ANALYZE public.user_roles;
ANALYZE public.case_types;

-- Add comments for documentation
COMMENT ON INDEX idx_leads_company_created_status_value IS 'Optimized index for dashboard lead queries with company filtering';
COMMENT ON INDEX idx_cases_company_created_status_type IS 'Optimized index for dashboard case queries with company filtering';
COMMENT ON INDEX idx_time_entries_company_created_billing IS 'Optimized index for time tracking and billing queries';
COMMENT ON INDEX idx_leads_search_company IS 'Full-text search index for leads within company';
COMMENT ON INDEX idx_cases_search_company IS 'Full-text search index for cases within company';

-- Create function to monitor index usage
CREATE OR REPLACE FUNCTION public.get_index_usage_stats()
RETURNS TABLE (
  schemaname text,
  tablename text,
  indexname text,
  idx_scan bigint,
  idx_tup_read bigint,
  idx_tup_fetch bigint
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.schemaname::text,
    s.tablename::text,
    s.indexrelname::text,
    s.idx_scan,
    s.idx_tup_read,
    s.idx_tup_fetch
  FROM pg_stat_user_indexes s
  WHERE s.schemaname = 'public'
  ORDER BY s.idx_scan DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.get_index_usage_stats() TO authenticated;

COMMENT ON FUNCTION public.get_index_usage_stats IS 'Monitor index usage statistics for performance optimization';
